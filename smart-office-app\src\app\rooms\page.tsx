'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Calendar, 
  Building2,
  Clock,
  Users,
  Monitor,
  Wifi,
  Coffee,
  Projector,
  Volume2,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface ConferenceRoom {
  id: string
  name: string
  capacity: number
  floor: number
  amenities: string[]
  is_available: boolean
  current_booking?: {
    title: string
    start_time: string
    end_time: string
    organizer: string
  }
}

export default function RoomsPage() {
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null)
  const [bookingTitle, setBookingTitle] = useState('')
  const [startTime, setStartTime] = useState('')
  const [endTime, setEndTime] = useState('')
  const [currentTime] = useState(new Date())
  
  // Mock room data
  const [rooms] = useState<ConferenceRoom[]>([
    {
      id: '1',
      name: 'Meeting Room A',
      capacity: 8,
      floor: 1,
      amenities: ['projector', 'whiteboard', 'video_conference'],
      is_available: true
    },
    {
      id: '2',
      name: 'Meeting Room B',
      capacity: 12,
      floor: 1,
      amenities: ['projector', 'whiteboard'],
      is_available: false,
      current_booking: {
        title: 'Team Standup',
        start_time: '10:00 AM',
        end_time: '11:00 AM',
        organizer: 'John Doe'
      }
    },
    {
      id: '3',
      name: 'Conference Hall',
      capacity: 50,
      floor: 2,
      amenities: ['projector', 'sound_system', 'video_conference', 'catering'],
      is_available: true
    },
    {
      id: '4',
      name: 'Small Room 1',
      capacity: 4,
      floor: 2,
      amenities: ['whiteboard'],
      is_available: true
    },
    {
      id: '5',
      name: 'Small Room 2',
      capacity: 4,
      floor: 2,
      amenities: ['whiteboard', 'tv_screen'],
      is_available: false,
      current_booking: {
        title: '1:1 Meeting',
        start_time: '2:00 PM',
        end_time: '3:00 PM',
        organizer: 'Jane Smith'
      }
    }
  ])

  const getAmenityIcon = (amenity: string) => {
    switch (amenity) {
      case 'projector': return <Projector className="h-4 w-4" />
      case 'whiteboard': return <Monitor className="h-4 w-4" />
      case 'video_conference': return <Wifi className="h-4 w-4" />
      case 'sound_system': return <Volume2 className="h-4 w-4" />
      case 'catering': return <Coffee className="h-4 w-4" />
      case 'tv_screen': return <Monitor className="h-4 w-4" />
      default: return <CheckCircle className="h-4 w-4" />
    }
  }

  const getAmenityLabel = (amenity: string) => {
    switch (amenity) {
      case 'projector': return 'Projector'
      case 'whiteboard': return 'Whiteboard'
      case 'video_conference': return 'Video Conference'
      case 'sound_system': return 'Sound System'
      case 'catering': return 'Catering'
      case 'tv_screen': return 'TV Screen'
      default: return amenity
    }
  }

  const handleBookRoom = () => {
    if (selectedRoom && bookingTitle && startTime && endTime) {
      // Here you would typically send data to your backend
      console.log('Booking room:', { selectedRoom, bookingTitle, startTime, endTime })
      alert('Room booked successfully!')
      setSelectedRoom(null)
      setBookingTitle('')
      setStartTime('')
      setEndTime('')
    }
  }

  const availableRooms = rooms.filter(room => room.is_available).length
  const bookedRooms = rooms.filter(room => !room.is_available).length

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">SmartOffice</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
              </div>
              <Button variant="outline" onClick={() => window.history.back()}>
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Meeting Rooms</h2>
          <p className="text-gray-600">Book conference rooms for your meetings and events</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available Now</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{availableRooms}</div>
              <p className="text-xs text-muted-foreground">Ready to book</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Currently Booked</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{bookedRooms}</div>
              <p className="text-xs text-muted-foreground">In use</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Rooms</CardTitle>
              <Building2 className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{rooms.length}</div>
              <p className="text-xs text-muted-foreground">Across all floors</p>
            </CardContent>
          </Card>
        </div>

        {/* Room Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {rooms.map((room) => (
            <Card 
              key={room.id} 
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedRoom === room.id ? 'ring-2 ring-blue-500' : ''
              } ${!room.is_available ? 'opacity-75' : ''}`}
              onClick={() => room.is_available && setSelectedRoom(room.id)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{room.name}</CardTitle>
                  {room.is_available ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                </div>
                <CardDescription>
                  Floor {room.floor} • Capacity: {room.capacity} people
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Amenities */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Amenities</h4>
                  <div className="flex flex-wrap gap-2">
                    {room.amenities.map((amenity) => (
                      <div key={amenity} className="flex items-center space-x-1 bg-gray-100 rounded-full px-2 py-1">
                        {getAmenityIcon(amenity)}
                        <span className="text-xs">{getAmenityLabel(amenity)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Current Booking */}
                {!room.is_available && room.current_booking && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-red-800 mb-1">Currently Booked</h4>
                    <p className="text-sm text-red-700">{room.current_booking.title}</p>
                    <p className="text-xs text-red-600">
                      {room.current_booking.start_time} - {room.current_booking.end_time}
                    </p>
                    <p className="text-xs text-red-600">by {room.current_booking.organizer}</p>
                  </div>
                )}

                {/* Available Status */}
                {room.is_available && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-green-800">Available</h4>
                    <p className="text-xs text-green-600">Ready for booking</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Booking Form */}
        {selectedRoom && (
          <Card>
            <CardHeader>
              <CardTitle>Book {rooms.find(r => r.id === selectedRoom)?.name}</CardTitle>
              <CardDescription>Fill in the details for your meeting</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Meeting Title</label>
                <Input
                  placeholder="Enter meeting title"
                  value={bookingTitle}
                  onChange={(e) => setBookingTitle(e.target.value)}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Start Time</label>
                  <Input
                    type="datetime-local"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">End Time</label>
                  <Input
                    type="datetime-local"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <Button variant="outline" onClick={() => setSelectedRoom(null)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleBookRoom}
                  disabled={!bookingTitle || !startTime || !endTime}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Room
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
