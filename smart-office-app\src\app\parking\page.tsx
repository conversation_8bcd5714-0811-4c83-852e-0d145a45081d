'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Car, 
  Building2,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface ParkingSlot {
  id: string
  slot_number: string
  floor: number
  is_occupied: boolean
  occupied_by?: string
  reserved_by?: string
  reservation_time?: string
}

export default function ParkingPage() {
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null)
  const [currentTime] = useState(new Date())
  
  // Mock parking data
  const [parkingSlots] = useState<ParkingSlot[]>([
    { id: '1', slot_number: 'A1', floor: 1, is_occupied: true, occupied_by: '<PERSON>' },
    { id: '2', slot_number: 'A2', floor: 1, is_occupied: false },
    { id: '3', slot_number: 'A3', floor: 1, is_occupied: false },
    { id: '4', slot_number: 'A4', floor: 1, is_occupied: true, occupied_by: '<PERSON>' },
    { id: '5', slot_number: 'A5', floor: 1, is_occupied: false },
    { id: '6', slot_number: 'B1', floor: 2, is_occupied: false },
    { id: '7', slot_number: 'B2', floor: 2, is_occupied: true, occupied_by: 'Mike Johnson' },
    { id: '8', slot_number: 'B3', floor: 2, is_occupied: false, reserved_by: 'You', reservation_time: '09:30 AM' },
    { id: '9', slot_number: 'B4', floor: 2, is_occupied: false },
    { id: '10', slot_number: 'B5', floor: 2, is_occupied: false },
  ])

  const getSlotStatus = (slot: ParkingSlot) => {
    if (slot.is_occupied) return 'occupied'
    if (slot.reserved_by) return 'reserved'
    return 'available'
  }

  const getSlotColor = (slot: ParkingSlot) => {
    const status = getSlotStatus(slot)
    switch (status) {
      case 'occupied': return 'bg-red-100 border-red-300 text-red-800'
      case 'reserved': return 'bg-yellow-100 border-yellow-300 text-yellow-800'
      case 'available': return 'bg-green-100 border-green-300 text-green-800 hover:bg-green-200'
      default: return 'bg-gray-100 border-gray-300 text-gray-800'
    }
  }

  const getSlotIcon = (slot: ParkingSlot) => {
    const status = getSlotStatus(slot)
    switch (status) {
      case 'occupied': return <XCircle className="h-4 w-4" />
      case 'reserved': return <AlertCircle className="h-4 w-4" />
      case 'available': return <CheckCircle className="h-4 w-4" />
      default: return <Car className="h-4 w-4" />
    }
  }

  const handleSlotSelect = (slotId: string) => {
    const slot = parkingSlots.find(s => s.id === slotId)
    if (slot && getSlotStatus(slot) === 'available') {
      setSelectedSlot(slotId)
    }
  }

  const handleReserveSlot = () => {
    if (selectedSlot) {
      // Here you would typically send data to your backend
      console.log('Reserving slot:', selectedSlot)
      alert('Parking slot reserved successfully!')
      setSelectedSlot(null)
    }
  }

  const floorSlots = (floor: number) => parkingSlots.filter(slot => slot.floor === floor)
  const availableSlots = parkingSlots.filter(slot => getSlotStatus(slot) === 'available').length
  const occupiedSlots = parkingSlots.filter(slot => slot.is_occupied).length
  const reservedSlots = parkingSlots.filter(slot => slot.reserved_by && !slot.is_occupied).length

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">SmartOffice</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
              </div>
              <Button variant="outline" onClick={() => window.history.back()}>
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Parking Management</h2>
          <p className="text-gray-600">View and reserve available parking slots</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{availableSlots}</div>
              <p className="text-xs text-muted-foreground">Ready to reserve</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupied</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{occupiedSlots}</div>
              <p className="text-xs text-muted-foreground">Currently in use</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reserved</CardTitle>
              <AlertCircle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{reservedSlots}</div>
              <p className="text-xs text-muted-foreground">Temporarily held</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Slots</CardTitle>
              <Car className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{parkingSlots.length}</div>
              <p className="text-xs text-muted-foreground">Across all floors</p>
            </CardContent>
          </Card>
        </div>

        {/* Legend */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
                <span className="text-sm">Available</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                <span className="text-sm">Occupied</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded"></div>
                <span className="text-sm">Reserved</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Floor 1 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              Floor 1
            </CardTitle>
            <CardDescription>Ground floor parking slots</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-4">
              {floorSlots(1).map((slot) => (
                <div
                  key={slot.id}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getSlotColor(slot)} ${
                    selectedSlot === slot.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => handleSlotSelect(slot.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold">{slot.slot_number}</span>
                    {getSlotIcon(slot)}
                  </div>
                  <div className="text-xs">
                    {slot.is_occupied && slot.occupied_by && (
                      <span>Occupied by {slot.occupied_by}</span>
                    )}
                    {slot.reserved_by && !slot.is_occupied && (
                      <span>Reserved by {slot.reserved_by}</span>
                    )}
                    {getSlotStatus(slot) === 'available' && <span>Available</span>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Floor 2 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              Floor 2
            </CardTitle>
            <CardDescription>Second floor parking slots</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-4">
              {floorSlots(2).map((slot) => (
                <div
                  key={slot.id}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${getSlotColor(slot)} ${
                    selectedSlot === slot.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => handleSlotSelect(slot.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold">{slot.slot_number}</span>
                    {getSlotIcon(slot)}
                  </div>
                  <div className="text-xs">
                    {slot.is_occupied && slot.occupied_by && (
                      <span>Occupied by {slot.occupied_by}</span>
                    )}
                    {slot.reserved_by && !slot.is_occupied && (
                      <span>Reserved by {slot.reserved_by}</span>
                    )}
                    {getSlotStatus(slot) === 'available' && <span>Available</span>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Reserve Button */}
        {selectedSlot && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">
                  Reserve Slot {parkingSlots.find(s => s.id === selectedSlot)?.slot_number}?
                </h3>
                <p className="text-gray-600 mb-4">
                  This slot will be reserved for you for the next 30 minutes
                </p>
                <div className="flex justify-center space-x-4">
                  <Button variant="outline" onClick={() => setSelectedSlot(null)}>
                    Cancel
                  </Button>
                  <Button onClick={handleReserveSlot}>
                    <Car className="h-4 w-4 mr-2" />
                    Reserve Slot
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
