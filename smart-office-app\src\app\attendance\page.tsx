'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  MapPin, 
  Clock, 
  Building2,
  UserCheck,
  Home,
  Coffee,
  Car,
  Bike,
  Bus,
  Footprints,
  QrCode,
  Wifi
} from 'lucide-react'

export default function AttendancePage() {
  const [attendanceStatus, setAttendanceStatus] = useState<'wfh' | 'in-office' | 'on-leave' | null>(null)
  const [transportMode, setTransportMode] = useState<'car' | 'bike' | 'public' | 'walk' | null>(null)
  const [isCheckedIn, setIsCheckedIn] = useState(false)
  const [currentTime] = useState(new Date())

  const handleAttendanceSubmit = () => {
    setIsCheckedIn(true)
    // Here you would typically send data to your backend
    console.log('Attendance marked:', { attendanceStatus, transportMode })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in-office': return <Building2 className="h-5 w-5" />
      case 'wfh': return <Home className="h-5 w-5" />
      case 'on-leave': return <Coffee className="h-5 w-5" />
      default: return <UserCheck className="h-5 w-5" />
    }
  }

  const getTransportIcon = (mode: string) => {
    switch (mode) {
      case 'car': return <Car className="h-5 w-5" />
      case 'bike': return <Bike className="h-5 w-5" />
      case 'public': return <Bus className="h-5 w-5" />
      case 'walk': return <Footprints className="h-5 w-5" />
      default: return <Car className="h-5 w-5" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">SmartOffice</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
              </div>
              <Button variant="outline" onClick={() => window.history.back()}>
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Mark Attendance</h2>
          <p className="text-gray-600">Select your work status for today</p>
        </div>

        {!isCheckedIn ? (
          <div className="space-y-6">
            {/* Attendance Status Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Work Status</CardTitle>
                <CardDescription>Choose your work location for today</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant={attendanceStatus === 'in-office' ? 'default' : 'outline'}
                    className="h-20 flex flex-col space-y-2"
                    onClick={() => setAttendanceStatus('in-office')}
                  >
                    <Building2 className="h-6 w-6" />
                    <span>In Office</span>
                  </Button>
                  <Button
                    variant={attendanceStatus === 'wfh' ? 'default' : 'outline'}
                    className="h-20 flex flex-col space-y-2"
                    onClick={() => setAttendanceStatus('wfh')}
                  >
                    <Home className="h-6 w-6" />
                    <span>Work From Home</span>
                  </Button>
                  <Button
                    variant={attendanceStatus === 'on-leave' ? 'default' : 'outline'}
                    className="h-20 flex flex-col space-y-2"
                    onClick={() => setAttendanceStatus('on-leave')}
                  >
                    <Coffee className="h-6 w-6" />
                    <span>On Leave</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Transport Mode (only for in-office) */}
            {attendanceStatus === 'in-office' && (
              <Card>
                <CardHeader>
                  <CardTitle>Transport Mode</CardTitle>
                  <CardDescription>How are you commuting to the office?</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Button
                      variant={transportMode === 'car' ? 'default' : 'outline'}
                      className="h-16 flex flex-col space-y-1"
                      onClick={() => setTransportMode('car')}
                    >
                      <Car className="h-5 w-5" />
                      <span className="text-xs">Car</span>
                    </Button>
                    <Button
                      variant={transportMode === 'bike' ? 'default' : 'outline'}
                      className="h-16 flex flex-col space-y-1"
                      onClick={() => setTransportMode('bike')}
                    >
                      <Bike className="h-5 w-5" />
                      <span className="text-xs">Bike</span>
                    </Button>
                    <Button
                      variant={transportMode === 'public' ? 'default' : 'outline'}
                      className="h-16 flex flex-col space-y-1"
                      onClick={() => setTransportMode('public')}
                    >
                      <Bus className="h-5 w-5" />
                      <span className="text-xs">Public Transport</span>
                    </Button>
                    <Button
                      variant={transportMode === 'walk' ? 'default' : 'outline'}
                      className="h-16 flex flex-col space-y-1"
                      onClick={() => setTransportMode('walk')}
                    >
                      <Footprints className="h-5 w-5" />
                      <span className="text-xs">Walking</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Check-in Options */}
            {attendanceStatus === 'in-office' && (
              <Card>
                <CardHeader>
                  <CardTitle>Quick Check-in</CardTitle>
                  <CardDescription>Use one of these methods for faster check-in</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" className="h-16 flex flex-col space-y-2">
                      <QrCode className="h-6 w-6" />
                      <span className="text-sm">QR Code</span>
                    </Button>
                    <Button variant="outline" className="h-16 flex flex-col space-y-2">
                      <MapPin className="h-6 w-6" />
                      <span className="text-sm">Location</span>
                    </Button>
                    <Button variant="outline" className="h-16 flex flex-col space-y-2">
                      <Wifi className="h-6 w-6" />
                      <span className="text-sm">Office WiFi</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <Card>
              <CardContent className="pt-6">
                <Button 
                  className="w-full h-12 text-lg"
                  onClick={handleAttendanceSubmit}
                  disabled={!attendanceStatus || (attendanceStatus === 'in-office' && !transportMode)}
                >
                  <Clock className="h-5 w-5 mr-2" />
                  Mark Attendance
                </Button>
              </CardContent>
            </Card>
          </div>
        ) : (
          /* Success State */
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UserCheck className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Attendance Marked!</h3>
                <p className="text-gray-600 mb-4">
                  Your attendance has been successfully recorded for {currentTime.toLocaleDateString()}
                </p>
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-center space-x-4 text-sm">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(attendanceStatus!)}
                      <span className="capitalize">{attendanceStatus?.replace('-', ' ')}</span>
                    </div>
                    {transportMode && (
                      <>
                        <span>•</span>
                        <div className="flex items-center space-x-2">
                          {getTransportIcon(transportMode)}
                          <span className="capitalize">{transportMode === 'public' ? 'Public Transport' : transportMode}</span>
                        </div>
                      </>
                    )}
                    <span>•</span>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span>{currentTime.toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
                <Button onClick={() => window.history.back()}>
                  Back to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
