'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { QrCode, Camera, CheckCircle, XCircle } from 'lucide-react'

interface QRScannerProps {
  onScanSuccess: (data: string) => void
  onScanError: (error: string) => void
}

export function QRScanner({ onScanSuccess, onScanError }: QRScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Mock QR code scanning - in a real app, you'd use a camera library
  const startScanning = () => {
    setIsScanning(true)
    setError(null)
    
    // Simulate scanning process
    setTimeout(() => {
      const mockQRData = 'OFFICE_CHECKIN_' + Date.now()
      setScanResult(mockQRData)
      setIsScanning(false)
      onScanSuccess(mockQRData)
    }, 2000)
  }

  const stopScanning = () => {
    setIsScanning(false)
    setError(null)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <QrCode className="h-5 w-5 mr-2" />
          QR Code Check-in
        </CardTitle>
        <CardDescription>
          Scan the office QR code to automatically check in
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center">
          {!isScanning && !scanResult && (
            <div className="space-y-4">
              <div className="w-32 h-32 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
                <Camera className="h-12 w-12 text-gray-400" />
              </div>
              <Button onClick={startScanning} className="w-full">
                <QrCode className="h-4 w-4 mr-2" />
                Start QR Scan
              </Button>
            </div>
          )}

          {isScanning && (
            <div className="space-y-4">
              <div className="w-32 h-32 mx-auto bg-blue-100 rounded-lg flex items-center justify-center animate-pulse">
                <QrCode className="h-12 w-12 text-blue-600" />
              </div>
              <p className="text-sm text-gray-600">Scanning for QR code...</p>
              <Button variant="outline" onClick={stopScanning}>
                Cancel Scan
              </Button>
            </div>
          )}

          {scanResult && (
            <div className="space-y-4">
              <div className="w-32 h-32 mx-auto bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
              <p className="text-sm text-green-600 font-medium">QR Code Scanned Successfully!</p>
              <p className="text-xs text-gray-500">Check-in data: {scanResult}</p>
              <Button 
                variant="outline" 
                onClick={() => {
                  setScanResult(null)
                  setError(null)
                }}
              >
                Scan Again
              </Button>
            </div>
          )}

          {error && (
            <div className="space-y-4">
              <div className="w-32 h-32 mx-auto bg-red-100 rounded-lg flex items-center justify-center">
                <XCircle className="h-12 w-12 text-red-600" />
              </div>
              <p className="text-sm text-red-600 font-medium">Scan Failed</p>
              <p className="text-xs text-gray-500">{error}</p>
              <Button 
                variant="outline" 
                onClick={() => {
                  setError(null)
                  setScanResult(null)
                }}
              >
                Try Again
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function QRCodeGenerator({ data }: { data: string }) {
  // In a real app, you'd use a QR code generation library
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(data)}`

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <QrCode className="h-5 w-5 mr-2" />
          Office Check-in QR Code
        </CardTitle>
        <CardDescription>
          Scan this code to check in to the office
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center space-y-4">
          <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center">
            <img 
              src={qrCodeUrl} 
              alt="QR Code" 
              className="w-40 h-40"
              onError={(e) => {
                // Fallback if QR service is unavailable
                const target = e.target as HTMLImageElement
                target.style.display = 'none'
                target.parentElement!.innerHTML = `
                  <div class="w-40 h-40 bg-gray-100 rounded flex items-center justify-center">
                    <div class="text-center">
                      <div class="text-2xl font-mono mb-2">QR</div>
                      <div class="text-xs text-gray-500">Code</div>
                    </div>
                  </div>
                `
              }}
            />
          </div>
          <p className="text-sm text-gray-600">
            Point your camera at this QR code to check in
          </p>
          <p className="text-xs text-gray-500 font-mono">
            Data: {data}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
