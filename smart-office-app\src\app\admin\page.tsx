'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Car, 
  Calendar, 
  Building2,
  UserCheck,
  Home,
  Coffee,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'

export default function AdminPage() {
  const [currentTime] = useState(new Date())
  
  // Mock admin data
  const [stats] = useState({
    total_employees: 150,
    present_today: 89,
    wfh_today: 45,
    on_leave_today: 16,
    parking_occupied: 67,
    parking_available: 33,
    rooms_booked_today: 12,
    rooms_available: 8,
    attendance_trend: '+5.2%',
    parking_utilization: '67%',
    room_utilization: '60%'
  })

  const [recentActivity] = useState([
    { id: 1, type: 'attendance', message: '<PERSON> checked in', time: '2 minutes ago', status: 'success' },
    { id: 2, type: 'parking', message: 'Parking slot B3 reserved by <PERSON>', time: '5 minutes ago', status: 'info' },
    { id: 3, type: 'room', message: 'Meeting Room A booked for Team Standup', time: '8 minutes ago', status: 'info' },
    { id: 4, type: 'attendance', message: '<PERSON> <PERSON> marked WFH', time: '12 minutes ago', status: 'warning' },
    { id: 5, type: 'room', message: 'Conference Hall booking cancelled', time: '15 minutes ago', status: 'error' },
  ])

  const [weeklyData] = useState([
    { day: 'Mon', attendance: 85, parking: 70, rooms: 8 },
    { day: 'Tue', attendance: 92, parking: 75, rooms: 12 },
    { day: 'Wed', attendance: 88, parking: 68, rooms: 10 },
    { day: 'Thu', attendance: 95, parking: 80, rooms: 15 },
    { day: 'Fri', attendance: 78, parking: 60, rooms: 6 },
  ])

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'attendance': return <UserCheck className="h-4 w-4" />
      case 'parking': return <Car className="h-4 w-4" />
      case 'room': return <Calendar className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'error': return 'text-red-600'
      case 'info': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  const attendancePercentage = Math.round((stats.present_today / stats.total_employees) * 100)
  const parkingPercentage = Math.round((stats.parking_occupied / (stats.parking_occupied + stats.parking_available)) * 100)
  const roomPercentage = Math.round((stats.rooms_booked_today / (stats.rooms_booked_today + stats.rooms_available)) * 100)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">SmartOffice Admin</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
              </div>
              <Button variant="outline" onClick={() => window.history.back()}>
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h2>
          <p className="text-gray-600">Monitor and manage office operations</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_employees}</div>
              <p className="text-xs text-muted-foreground">
                Registered in system
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{attendancePercentage}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.attendance_trend} from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Parking Utilization</CardTitle>
              <Car className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{parkingPercentage}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.parking_occupied} of {stats.parking_occupied + stats.parking_available} slots
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Room Utilization</CardTitle>
              <Calendar className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{roomPercentage}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.rooms_booked_today} of {stats.rooms_booked_today + stats.rooms_available} rooms
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Attendance Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserCheck className="h-5 w-5 mr-2" />
                Attendance Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-green-600" />
                    <span className="text-sm">In Office</span>
                  </div>
                  <span className="font-semibold">{stats.present_today}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Home className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">Work From Home</span>
                  </div>
                  <span className="font-semibold">{stats.wfh_today}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Coffee className="h-4 w-4 text-orange-600" />
                    <span className="text-sm">On Leave</span>
                  </div>
                  <span className="font-semibold">{stats.on_leave_today}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Weekly Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Weekly Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {weeklyData.map((day) => (
                  <div key={day.day} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{day.day}</span>
                    <div className="flex space-x-2 text-xs">
                      <span className="text-green-600">{day.attendance}% att</span>
                      <span className="text-purple-600">{day.parking}% park</span>
                      <span className="text-orange-600">{day.rooms} rooms</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Employees
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Car className="h-4 w-4 mr-2" />
                  Parking Settings
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Room Management
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Generate Reports
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest system events and user actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                  <div className={`${getActivityColor(activity.status)}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' :
                    activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                  }`}></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
