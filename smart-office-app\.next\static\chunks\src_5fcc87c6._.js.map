{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/smart_office_app/smart-office-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/smart_office_app/smart-office-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/smart_office_app/smart-office-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/smart_office_app/smart-office-app/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { \n  Building2, \n  Home, \n  UserCheck, \n  Car, \n  Calendar, \n  BarChart3, \n  Settings,\n  Bell,\n  User\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Attendance', href: '/attendance', icon: UserCheck },\n  { name: 'Parking', href: '/parking', icon: Car },\n  { name: 'Meeting Rooms', href: '/rooms', icon: Calendar },\n  { name: 'Admin', href: '/admin', icon: BarChart3 },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <header className=\"bg-white shadow-sm border-b sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <Building2 className=\"h-8 w-8 text-blue-600 mr-3\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">SmartOffice</h1>\n          </Link>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                  )}\n                >\n                  <item.icon className=\"h-4 w-4 mr-2\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"icon\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n            <Button variant=\"ghost\" size=\"icon\">\n              <Settings className=\"h-5 w-5\" />\n            </Button>\n            <Button variant=\"ghost\" size=\"icon\">\n              <User className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden border-t\">\n        <div className=\"px-2 pt-2 pb-3 space-y-1\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors',\n                  isActive\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                )}\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,mNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mMAAA,CAAA,MAAG;IAAC;IAC/C;QAAE,MAAM;QAAiB,MAAM;QAAU,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qNAAA,CAAA,YAAS;IAAC;CAClD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAInD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,8BACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kFACA,WACI,8BACA;;8CAGN,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;gCACpB,KAAK,IAAI;;2BAVL,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;;;;;;;AAKV;GA3EgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/smart_office_app/smart-office-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Navigation } from '@/components/layout/navigation'\nimport {\n  Users,\n  Car,\n  Calendar,\n  MapPin,\n  Clock,\n  Building2,\n  UserCheck,\n  UserX,\n  Home,\n  Coffee\n} from 'lucide-react'\n\ninterface DashboardStats {\n  total_employees: number\n  present_today: number\n  wfh_today: number\n  on_leave_today: number\n  parking_occupied: number\n  parking_available: number\n  rooms_booked_today: number\n  rooms_available: number\n}\n\nexport default function Home() {\n  const [stats, setStats] = useState<DashboardStats>({\n    total_employees: 150,\n    present_today: 89,\n    wfh_today: 45,\n    on_leave_today: 16,\n    parking_occupied: 67,\n    parking_available: 33,\n    rooms_booked_today: 12,\n    rooms_available: 8\n  })\n\n  const [currentTime, setCurrentTime] = useState(new Date())\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date())\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [])\n\n  const attendancePercentage = Math.round((stats.present_today / stats.total_employees) * 100)\n  const parkingPercentage = Math.round((stats.parking_occupied / (stats.parking_occupied + stats.parking_available)) * 100)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <Navigation />\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Welcome back!</h2>\n          <p className=\"text-gray-600\">Here's what's happening in your office today.</p>\n          <div className=\"mt-4 text-sm text-gray-500\">\n            {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}\n          </div>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {/* Attendance Stats */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Present Today</CardTitle>\n              <UserCheck className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{stats.present_today}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {attendancePercentage}% of total employees\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Work From Home</CardTitle>\n              <Home className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{stats.wfh_today}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Remote workers today\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">On Leave</CardTitle>\n              <Coffee className=\"h-4 w-4 text-orange-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-orange-600\">{stats.on_leave_today}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Employees on leave\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Parking Occupied</CardTitle>\n              <Car className=\"h-4 w-4 text-purple-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-purple-600\">{stats.parking_occupied}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {parkingPercentage}% of parking slots\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <Link href=\"/attendance\">\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Clock className=\"h-5 w-5 mr-2 text-blue-600\" />\n                  Attendance\n                </CardTitle>\n                <CardDescription>\n                  Mark your daily attendance and track your work status\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button className=\"w-full\">\n                  <UserCheck className=\"h-4 w-4 mr-2\" />\n                  Mark Attendance\n                </Button>\n              </CardContent>\n            </Card>\n          </Link>\n\n          <Link href=\"/parking\">\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Car className=\"h-5 w-5 mr-2 text-purple-600\" />\n                  Parking\n                </CardTitle>\n                <CardDescription>\n                  View available parking slots and reserve your spot\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button className=\"w-full\" variant=\"outline\">\n                  <MapPin className=\"h-4 w-4 mr-2\" />\n                  View Parking\n                </Button>\n              </CardContent>\n            </Card>\n          </Link>\n\n          <Link href=\"/rooms\">\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer h-full\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Calendar className=\"h-5 w-5 mr-2 text-green-600\" />\n                  Meeting Rooms\n                </CardTitle>\n                <CardDescription>\n                  Book conference rooms for your meetings and events\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button className=\"w-full\" variant=\"outline\">\n                  <Building2 className=\"h-4 w-4 mr-2\" />\n                  Book Room\n                </Button>\n              </CardContent>\n            </Card>\n          </Link>\n        </div>\n\n        {/* Recent Activity */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Recent Activity</CardTitle>\n            <CardDescription>\n              Latest updates from your office\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium\">John Doe checked in</p>\n                  <p className=\"text-xs text-gray-500\">2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium\">Meeting Room A booked</p>\n                  <p className=\"text-xs text-gray-500\">5 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium\">Parking slot B3 reserved</p>\n                  <p className=\"text-xs text-gray-500\">10 minutes ago</p>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </main>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA+Be,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,iBAAiB;IACnB;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ;wCAAY;oBACxB,eAAe,IAAI;gBACrB;uCAAG;YAEH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,uBAAuB,KAAK,KAAK,CAAC,AAAC,MAAM,aAAa,GAAG,MAAM,eAAe,GAAI;IACxF,MAAM,oBAAoB,KAAK,KAAK,CAAC,AAAC,MAAM,gBAAgB,GAAG,CAAC,MAAM,gBAAgB,GAAG,MAAM,iBAAiB,IAAK;IAErH,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,6LAAC;gCAAI,WAAU;;oCACZ,YAAY,kBAAkB;oCAAG;oCAAI,YAAY,kBAAkB;;;;;;;;;;;;;kCAKxE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;kDAEvB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAqC,MAAM,aAAa;;;;;;0DACvE,6LAAC;gDAAE,WAAU;;oDACV;oDAAqB;;;;;;;;;;;;;;;;;;;0CAK5B,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAoC,MAAM,SAAS;;;;;;0DAClE,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;kDAEpB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAsC,MAAM,cAAc;;;;;;0DACzE,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;kDAEjB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAsC,MAAM,gBAAgB;;;;;;0DAC3E,6LAAC;gDAAE,WAAU;;oDACV;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAGlD,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAO9C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGlD,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;gDAAS,SAAQ;;kEACjC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAgC;;;;;;;8DAGtD,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;gDAAS,SAAQ;;kEACjC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;GApMwB;KAAA", "debugId": null}}]}