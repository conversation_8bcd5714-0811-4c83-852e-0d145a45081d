# SmartOffice - Streamline the Workplace

A comprehensive Smart Office Assistant App built for the hackathon challenge. This application enables IT employees to manage daily office operations including attendance tracking, parking management, and conference room booking with AI-driven insights.

## 🚀 Features

### Core Features
- **📊 Dashboard**: Real-time overview of office statistics and activities
- **✅ Attendance Management**: Mark daily attendance with multiple options (WFH, In-Office, On-Leave)
- **🚗 Parking Management**: View and reserve available parking slots in real-time
- **🏢 Meeting Room Booking**: Book conference rooms with live availability calendar
- **👨‍💼 Admin Dashboard**: Comprehensive analytics and management tools

### Advanced Features
- **🤖 AI Insights**: Predictive analytics and smart recommendations
- **📱 QR Code Check-in**: Quick attendance marking via QR codes
- **🔔 Real-time Notifications**: Live updates and alerts
- **📈 Analytics**: Attendance trends, parking utilization, and room occupancy
- **🎯 Auto-Resolution**: AI-powered conflict resolution for double bookings

### AI-Driven Features (Bonus)
- **📈 Peak Day Predictions**: AI suggests optimal office attendance days
- **🏢 Room Recommendations**: Smart room suggestions based on usage patterns
- **🔄 Auto-Resolution**: Automatic handling of booking conflicts
- **⏰ No-Show Detection**: Automatic release of unused rooms

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **Recharts** - Data visualization

### Backend & Database
- **Supabase** - Backend-as-a-Service (PostgreSQL + Auth + Real-time)
- **Prisma** - Database ORM (optional)
- **Socket.io** - Real-time communication

### Additional Services
- **Vercel** - Deployment platform
- **QR Code API** - QR code generation
- **Geolocation API** - Location-based check-in

## 📦 Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (optional for full functionality)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smart-office-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Database Setup (Optional)**
   If using Supabase, run the SQL schema from `src/lib/supabase.ts` in your Supabase SQL editor.

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open the application**
   Navigate to `http://localhost:3000` in your browser.

## 🏗️ Project Structure

```
smart-office-app/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── page.tsx           # Dashboard
│   │   ├── attendance/        # Attendance management
│   │   ├── parking/           # Parking management
│   │   ├── rooms/             # Meeting room booking
│   │   ├── admin/             # Admin dashboard
│   │   └── ai-insights/       # AI features
│   ├── components/
│   │   ├── ui/                # Reusable UI components
│   │   ├── layout/            # Layout components
│   │   └── features/          # Feature-specific components
│   ├── lib/                   # Utilities and configurations
│   ├── types/                 # TypeScript type definitions
│   └── hooks/                 # Custom React hooks
├── public/                    # Static assets
└── README.md
```

## 🎯 Usage

### For Employees
1. **Mark Attendance**: Choose work status (In-Office, WFH, On-Leave)
2. **Reserve Parking**: View available slots and reserve your spot
3. **Book Rooms**: Find and book meeting rooms with required amenities
4. **View Insights**: Check AI-powered recommendations and predictions

### For Admins
1. **Monitor Dashboard**: Track real-time office statistics
2. **Manage Resources**: Oversee parking and room utilization
3. **View Analytics**: Access detailed reports and trends
4. **Configure AI**: Customize AI features and notifications

## 🤖 AI Features

### Predictive Analytics
- **Attendance Forecasting**: Predict office occupancy for the week
- **Peak Hour Analysis**: Identify optimal times for parking and rooms
- **Trend Analysis**: Historical data insights and patterns

### Smart Recommendations
- **Room Suggestions**: AI recommends optimal rooms based on requirements
- **Parking Optimization**: Smart parking slot allocation
- **Schedule Optimization**: Suggest best times for meetings

### Auto-Resolution
- **Conflict Detection**: Automatically detect booking conflicts
- **Smart Rescheduling**: Suggest alternative times/rooms
- **No-Show Management**: Release unused bookings automatically

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Manual Deployment
```bash
npm run build
npm start
```

## 🔧 Configuration

### Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Optional: Production settings
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### AI Configuration
- Enable/disable AI features in the AI Insights page
- Configure prediction accuracy thresholds
- Set up auto-resolution preferences

## 📊 Database Schema

The application uses the following main tables:
- `users` - Employee information and roles
- `attendance_records` - Daily attendance tracking
- `parking_slots` - Parking space management
- `conference_rooms` - Meeting room details
- `room_bookings` - Room reservation records

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🏆 Hackathon Deliverables

✅ **Working Prototype**: Fully functional web application
✅ **Source Code**: Available on GitHub with comprehensive documentation
✅ **Product Roadmap**: Detailed feature development plan
✅ **Innovation**: AI-driven insights and predictions
✅ **Technical Implementation**: Modern tech stack with scalable architecture
✅ **UI/UX Design**: Intuitive and responsive design
✅ **Multi-user Support**: Role-based access control

## 📞 Support

For questions or support, please open an issue in the GitHub repository.

---

Built with ❤️ for the Smart Office Hackathon
