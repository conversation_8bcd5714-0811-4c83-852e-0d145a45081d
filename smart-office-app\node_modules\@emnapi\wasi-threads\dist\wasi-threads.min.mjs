const e="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0,t="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node;function r(e){return"function"==typeof(null==e?void 0:e.postMessage)?e.postMessage:"function"==typeof postMessage?postMessage:void 0}function s(e){return"function"==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(e)}function o(t){try{return t instanceof e.RuntimeError}catch(e){return!1}}function n(e,t){return{__emnapi__:{type:e,payload:t}}}function i(e){if(e){if(!s(e.buffer))throw new Error("Multithread features require shared wasm memory. Try to compile with `-matomics -mbulk-memory` and use `--import-memory --shared-memory` during linking, then create WebAssembly.Memory with `shared: true` option")}else if("undefined"==typeof SharedArrayBuffer)throw new Error("Current environment does not support SharedArrayBuffer, threads are not available!")}let a=0;class d{get nextWorkerID(){return a}constructor(e){var t;if(this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.wasmModule=null,this.wasmMemory=null,this.messageEvents=new WeakMap,!e)throw new TypeError("ThreadManager(): options is not provided");this._childThread="childThread"in e&&Boolean(e.childThread),this._childThread?(this._onCreateWorker=void 0,this._reuseWorker=!1,this._beforeLoad=void 0):(this._onCreateWorker=e.onCreateWorker,this._reuseWorker=function(e){var t;if("boolean"==typeof e)return!!e&&{size:0,strict:!1};if("number"==typeof e){if(!(e>=0))throw new RangeError("reuseWorker: size must be a non-negative integer");return{size:e,strict:!1}}if(!e)return!1;const r=null!==(t=Number(e.size))&&void 0!==t?t:0,s=Boolean(e.strict);if(!(r>0)&&s)throw new RangeError("reuseWorker: size must be set to positive integer if strict is set to true");return{size:r,strict:s}}(e.reuseWorker),this._beforeLoad=e.beforeLoad),this.printErr=null!==(t=e.printErr)&&void 0!==t?t:console.error.bind(console)}init(){this._childThread||this.initMainThread()}initMainThread(){this.preparePool()}preparePool(){if(this._reuseWorker&&this._reuseWorker.size){let e=this._reuseWorker.size;for(;e--;){const e=this.allocateUnusedWorker();t&&(e.once("message",(()=>{})),e.unref())}}}shouldPreloadWorkers(){return!this._childThread&&this._reuseWorker&&this._reuseWorker.size>0}loadWasmModuleToAllWorkers(){const e=Array(this.unusedWorkers.length);for(let r=0;r<this.unusedWorkers.length;++r){const s=this.unusedWorkers[r];t&&s.ref(),e[r]=this.loadWasmModuleToWorker(s).then((e=>(t&&s.unref(),e)),(e=>{throw t&&s.unref(),e}))}return Promise.all(e).catch((e=>{throw this.terminateAllThreads(),e}))}preloadWorkers(){return this.shouldPreloadWorkers()?this.loadWasmModuleToAllWorkers():Promise.resolve([])}setup(e,t){this.wasmModule=e,this.wasmMemory=t}markId(e){if(e.__emnapi_tid)return e.__emnapi_tid;const t=a+43;return a=(a+1)%536870869,this.pthreads[t]=e,e.__emnapi_tid=t,t}returnWorkerToPool(e){var r=e.__emnapi_tid;void 0!==r&&delete this.pthreads[r],this.unusedWorkers.push(e),this.runningWorkers.splice(this.runningWorkers.indexOf(e),1),delete e.__emnapi_tid,t&&e.unref()}loadWasmModuleToWorker(e,r){if(e.whenLoaded)return e.whenLoaded;const s=this.printErr,o=this._beforeLoad,a=this;return e.whenLoaded=new Promise(((d,h)=>{const l=r=>{if(r.__emnapi__){const s=r.__emnapi__.type,o=r.__emnapi__.payload;"loaded"===s?(e.loaded=!0,t&&!e.__emnapi_tid&&e.unref(),d(e)):"cleanup-thread"===s&&o.tid in this.pthreads&&this.cleanThread(e,o.tid)}};e.onmessage=t=>{l(t.data),this.fireMessageEvent(e,t)},e.onerror=function(t){let r="worker sent an error!";if(void 0!==e.__emnapi_tid&&(r="worker (tid = "+e.__emnapi_tid+") sent an error!"),s(r+" "+t.message),-1!==t.message.indexOf("RuntimeError")||-1!==t.message.indexOf("unreachable"))try{a.terminateAllThreads()}catch(e){}throw h(t),t},t&&(e.on("message",(function(t){var r,s;null===(s=(r=e).onmessage)||void 0===s||s.call(r,{data:t})})),e.on("error",(function(t){var r,s;null===(s=(r=e).onerror)||void 0===s||s.call(r,t)})),e.on("detachedExit",(function(){}))),"function"==typeof o&&o(e);try{e.postMessage(n("load",{wasmModule:this.wasmModule,wasmMemory:this.wasmMemory,sab:r}))}catch(e){throw i(this.wasmMemory),e}})),e.whenLoaded}allocateUnusedWorker(){const e=this._onCreateWorker;if("function"!=typeof e)throw new TypeError("`options.onCreateWorker` is not provided");const t=e({type:"thread",name:"emnapi-pthread"});return this.unusedWorkers.push(t),t}getNewWorker(e){if(this._reuseWorker){if(0===this.unusedWorkers.length){if(this._reuseWorker.strict&&!t){return void(0,this.printErr)("Tried to spawn a new thread, but the thread pool is exhausted.\nThis might result in a deadlock unless some threads eventually exit or the code explicitly breaks out to the event loop.")}const r=this.allocateUnusedWorker();this.loadWasmModuleToWorker(r,e)}return this.unusedWorkers.pop()}const r=this.allocateUnusedWorker();return this.loadWasmModuleToWorker(r,e),this.unusedWorkers.pop()}cleanThread(e,t,r){if(!r&&this._reuseWorker)this.returnWorkerToPool(e);else{delete this.pthreads[t];const r=this.runningWorkers.indexOf(e);-1!==r&&this.runningWorkers.splice(r,1),this.terminateWorker(e),delete e.__emnapi_tid}}terminateWorker(e){var t;const r=e.__emnapi_tid;e.terminate(),null===(t=this.messageEvents.get(e))||void 0===t||t.clear(),this.messageEvents.delete(e),e.onmessage=e=>{if(e.data.__emnapi__){(0,this.printErr)('received "'+e.data.__emnapi__.type+'" command from terminated worker: '+r)}}}terminateAllThreads(){for(let e=0;e<this.runningWorkers.length;++e)this.terminateWorker(this.runningWorkers[e]);for(let e=0;e<this.unusedWorkers.length;++e)this.terminateWorker(this.unusedWorkers[e]);this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.preparePool()}addMessageEventListener(e,t){let r=this.messageEvents.get(e);return r||(r=new Set,this.messageEvents.set(e,r)),r.add(t),()=>{null==r||r.delete(t)}}fireMessageEvent(e,t){const r=this.messageEvents.get(e);if(!r)return;const s=this.printErr;r.forEach((e=>{try{e(t)}catch(e){s(e.stack)}}))}}const h=Symbol("kIsProxy");function l(e,t){if(e[h])return e;const r=e.exports,s=function(e){const t=["apply","construct","defineProperty","deleteProperty","get","getOwnPropertyDescriptor","getPrototypeOf","has","isExtensible","ownKeys","preventExtensions","set","setPrototypeOf"],r={};for(let s=0;s<t.length;s++){const o=t[s];r[o]=function(){const t=Array.prototype.slice.call(arguments,1);return t.unshift(e),Reflect[o].apply(Reflect,t)}}return r}(r),o=()=>{},n=()=>0;s.get=function(e,s,i){var a;return"memory"===s?null!==(a="function"==typeof t?t():t)&&void 0!==a?a:Reflect.get(r,s,i):"_initialize"===s?s in r?o:void 0:"_start"===s?s in r?n:void 0:Reflect.get(r,s,i)},s.has=function(e,t){return"memory"===t||Reflect.has(r,t)};const i=new Proxy(Object.create(null),s);return new Proxy(e,{get:(e,t,r)=>"exports"===t?i:t===h||Reflect.get(e,t,r)})}const c=new WeakMap;class u{constructor(s){if(!s)throw new TypeError("WASIThreads(): options is not provided");if(!s.wasi)throw new TypeError("WASIThreads(): options.wasi is not provided");c.set(this,new WeakSet);const a=s.wasi;!function(e,t){const r=c.get(e);if(r.has(t))return;const s=e,n=t.wasiImport;if(n){const e=n.proc_exit;n.proc_exit=function(t){return s.terminateAllThreads(),e.call(this,t)}}const i=t.start;"function"==typeof i&&(t.start=function(e){try{return i.call(this,e)}catch(e){throw o(e)&&s.terminateAllThreads(),e}});r.add(t)}(this,a),this.wasi=a,this.childThread="childThread"in s&&Boolean(s.childThread),this.PThread=void 0,"threadManager"in s?"function"==typeof s.threadManager?this.PThread=s.threadManager():this.PThread=s.threadManager:this.childThread||(this.PThread=new d(s),this.PThread.init());let h=!1;"waitThreadStart"in s&&(h="number"==typeof s.waitThreadStart?s.waitThreadStart:Boolean(s.waitThreadStart));const l=r(s);if(this.childThread&&"function"!=typeof l)throw new TypeError("options.postMessage is not a function");this.postMessage=l;const u=Boolean(s.wasm64),f=e=>{if(e.data.__emnapi__){const t=e.data.__emnapi__.type,r=e.data.__emnapi__.payload;"spawn-thread"===t?p(r.startArg,r.errorOrTid):"terminate-all-threads"===t&&this.terminateAllThreads()}},p=(r,s)=>{var o;const a=void 0!==s;try{i(this.wasmMemory)}catch(e){if(null===(o=this.PThread)||void 0===o||o.printErr(e.stack),a){const e=new Int32Array(this.wasmMemory.buffer,s,2);return Atomics.store(e,0,1),Atomics.store(e,1,6),Atomics.notify(e,1),1}return-6}if(!a){const e=this.wasmInstance.exports.malloc;if(!(s=u?Number(e(BigInt(8))):e(8)))return-48}const d=this.wasmInstance.exports.free,c=u?e=>{d(BigInt(e))}:d,p=new Int32Array(this.wasmMemory.buffer,s,2);if(Atomics.store(p,0,0),Atomics.store(p,1,0),this.childThread){l(n("spawn-thread",{startArg:r,errorOrTid:s})),Atomics.wait(p,1,0);const e=Atomics.load(p,0),t=Atomics.load(p,1);return a?e:(c(s),e?-t:t)}const m=h||0===h;let w,y,_;m&&(w=new Int32Array(new SharedArrayBuffer(8208)),Atomics.store(w,0,0));const g=this.PThread;try{if(y=g.getNewWorker(w),!y)throw new Error("failed to get new worker");if(g.addMessageEventListener(y,f),_=g.markId(y),t&&y.ref(),y.postMessage(n("start",{tid:_,arg:r,sab:w})),m){if("number"==typeof h){if("timed-out"===Atomics.wait(w,0,0,h)){try{g.cleanThread(y,_,!0)}catch(e){}throw new Error("Spawning thread timed out. Please check if the worker is created successfully and if message is handled properly in the worker.")}}else Atomics.wait(w,0,0);if(Atomics.load(w,0)>1){try{g.cleanThread(y,_,!0)}catch(e){}throw function(t){var r,s;const o=new Int32Array(t);if(Atomics.load(o,0)<=1)return null;const n=Atomics.load(o,1),i=Atomics.load(o,2),a=Atomics.load(o,3),d=new Uint8Array(t),h=d.slice(16,16+n),l=d.slice(16+n,16+n+i),c=d.slice(16+n+i,16+n+i+a),u=(new TextDecoder).decode(h),f=(new TextDecoder).decode(l),p=(new TextDecoder).decode(c),m=new(null!==(r=globalThis[u])&&void 0!==r?r:"RuntimeError"===u&&null!==(s=e.RuntimeError)&&void 0!==s?s:Error)(f);return Object.defineProperty(m,"stack",{value:p,writable:!0,enumerable:!1,configurable:!0}),m}(w.buffer)}}}catch(e){return Atomics.store(p,0,1),Atomics.store(p,1,6),Atomics.notify(p,1),null==g||g.printErr(e.stack),a?1:(c(s),-6)}return Atomics.store(p,0,0),Atomics.store(p,1,_),Atomics.notify(p,1),g.runningWorkers.push(y),m||y.whenLoaded.catch((e=>{throw delete y.whenLoaded,g.cleanThread(y,_,!0),e})),a?0:(c(s),_)};this.threadSpawn=p}getImportObject(){return{wasi:{"thread-spawn":this.threadSpawn}}}setup(e,t,r){null!=r||(r=e.exports.memory),this.wasmInstance=e,this.wasmMemory=r,this.PThread&&this.PThread.setup(t,r)}preloadWorkers(){return this.PThread?this.PThread.preloadWorkers():Promise.resolve([])}initialize(e,t,r){const s=e.exports;null!=r||(r=s.memory),this.childThread&&(e=l(e,r)),this.setup(e,t,r);const o=this.wasi;if("_start"in s&&"function"==typeof s._start)if(this.childThread){o.start(e);try{o[f(o,"kStarted")]=!1}catch(e){}}else!function(e,t){const[r,s]=f(e,["kInstance","kSetMemory"]);e[r]=t,e[s](t.exports.memory)}(o,e);else o.initialize(e);return e}start(e,t,r){const s=e.exports;null!=r||(r=s.memory),this.childThread&&(e=l(e,r)),this.setup(e,t,r);return{exitCode:this.wasi.start(e),instance:e}}terminateAllThreads(){var e;this.childThread?this.postMessage(n("terminate-all-threads",{})):null===(e=this.PThread)||void 0===e||e.terminateAllThreads()}}function f(e,t){const r=Object.getOwnPropertySymbols(e),s=e=>t=>t.description?t.description===e:t.toString()===`Symbol(${e})`;return Array.isArray(t)?t.map((e=>r.filter(s(e))[0])):r.filter(s(t))[0]}class p{constructor(e){const t=r(e);if("function"!=typeof t)throw new TypeError("options.postMessage is not a function");this.postMessage=t,this.onLoad=null==e?void 0:e.onLoad,this.instance=void 0,this.messagesBeforeLoad=[]}instantiate(e){if("function"==typeof this.onLoad)return this.onLoad(e);throw new Error("ThreadMessageHandler.prototype.instantiate is not implemented")}handle(e){var t;if(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.__emnapi__){const t=e.data.__emnapi__.type,r=e.data.__emnapi__.payload;"load"===t?this._load(r):"start"===t&&this.handleAfterLoad(e,(()=>{this._start(r)}))}}_load(e){if(void 0!==this.instance)return;let t;try{t=this.instantiate(e)}catch(t){return void this._loaded(t,null,e)}const r=t&&"then"in t?t.then:void 0;"function"==typeof r?r.call(t,(t=>{this._loaded(null,t,e)}),(t=>{this._loaded(t,null,e)})):this._loaded(null,t,e)}_start(e){if("function"!=typeof this.instance.exports.wasi_thread_start){const t=new TypeError("wasi_thread_start is not exported");throw m(e.sab,2,t),t}const t=this.postMessage,r=e.tid,s=e.arg;m(e.sab,1);try{this.instance.exports.wasi_thread_start(r,s)}catch(e){throw o(e)&&t(n("terminate-all-threads",{})),e}t(n("cleanup-thread",{tid:r}))}_loaded(e,t,r){if(e)throw m(r.sab,2,e),e;if(null==t){const e=new TypeError("onLoad should return an object");throw m(r.sab,2,e),e}const s=t.instance;if(!s){const e=new TypeError('onLoad should return an object which includes "instance"');throw m(r.sab,2,e),e}this.instance=s;(0,this.postMessage)(n("loaded",{}));const o=this.messagesBeforeLoad;this.messagesBeforeLoad=[];for(let e=0;e<o.length;e++){const t=o[e];this.handle({data:t})}}handleAfterLoad(e,t){void 0!==this.instance?t.call(this,e):this.messagesBeforeLoad.push(e.data)}}function m(e,t,r){e&&(!function(e,t,r){const s=new Int32Array(e);if(Atomics.store(s,0,t),t>1&&r){const t=r.name,o=r.message,n=r.stack,i=(new TextEncoder).encode(t),a=(new TextEncoder).encode(o),d=(new TextEncoder).encode(n);Atomics.store(s,1,i.length),Atomics.store(s,2,a.length),Atomics.store(s,3,d.length);const h=new Uint8Array(e);h.set(i,16),h.set(a,16+i.length),h.set(d,16+i.length+a.length)}}(e.buffer,t,r),Atomics.notify(e,0))}export{d as ThreadManager,p as ThreadMessageHandler,u as WASIThreads,l as createInstanceProxy,s as isSharedArrayBuffer,o as isTrapError};
