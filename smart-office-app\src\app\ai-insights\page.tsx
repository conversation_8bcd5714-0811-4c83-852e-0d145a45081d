'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Navigation } from '@/components/layout/navigation'
import { 
  Brain, 
  TrendingUp, 
  Calendar, 
  Users, 
  Car,
  AlertTriangle,
  CheckCircle,
  Clock,
  Lightbulb,
  Target,
  BarChart3
} from 'lucide-react'

interface AIInsight {
  id: string
  type: 'prediction' | 'recommendation' | 'optimization' | 'alert'
  title: string
  description: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  category: 'attendance' | 'parking' | 'rooms' | 'general'
}

export default function AIInsightsPage() {
  const [currentTime] = useState(new Date())
  const [insights] = useState<AIInsight[]>([
    {
      id: '1',
      type: 'prediction',
      title: 'Peak Office Day Predicted',
      description: 'Thursday is predicted to have 95% office attendance based on historical patterns and upcoming meetings.',
      confidence: 87,
      impact: 'high',
      category: 'attendance'
    },
    {
      id: '2',
      type: 'recommendation',
      title: 'Optimal Room Suggestion',
      description: 'For your 2 PM meeting with 8 attendees, Meeting Room A is recommended based on past usage patterns and amenities.',
      confidence: 92,
      impact: 'medium',
      category: 'rooms'
    },
    {
      id: '3',
      type: 'optimization',
      title: 'Parking Optimization',
      description: 'Suggest employees arriving after 10 AM to use Floor 2 parking to reduce congestion on Floor 1.',
      confidence: 78,
      impact: 'medium',
      category: 'parking'
    },
    {
      id: '4',
      type: 'alert',
      title: 'Double Booking Detected',
      description: 'Conference Hall has overlapping bookings on Friday 3-4 PM. Auto-resolution suggested.',
      confidence: 95,
      impact: 'high',
      category: 'rooms'
    },
    {
      id: '5',
      type: 'prediction',
      title: 'Low Attendance Alert',
      description: 'Monday shows 40% lower attendance prediction. Consider promoting in-office collaboration events.',
      confidence: 83,
      impact: 'medium',
      category: 'attendance'
    }
  ])

  const [predictions] = useState({
    nextWeekAttendance: [
      { day: 'Mon', predicted: 65, actual: null, confidence: 85 },
      { day: 'Tue', predicted: 78, actual: null, confidence: 88 },
      { day: 'Wed', predicted: 82, actual: null, confidence: 90 },
      { day: 'Thu', predicted: 95, actual: null, confidence: 87 },
      { day: 'Fri', predicted: 58, actual: null, confidence: 82 },
    ],
    parkingPeakHours: [
      { hour: '8:00 AM', utilization: 45 },
      { hour: '9:00 AM', utilization: 85 },
      { hour: '10:00 AM', utilization: 95 },
      { hour: '11:00 AM', utilization: 78 },
      { hour: '12:00 PM', utilization: 65 },
    ]
  })

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'prediction': return <TrendingUp className="h-5 w-5" />
      case 'recommendation': return <Lightbulb className="h-5 w-5" />
      case 'optimization': return <Target className="h-5 w-5" />
      case 'alert': return <AlertTriangle className="h-5 w-5" />
      default: return <Brain className="h-5 w-5" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'prediction': return 'text-blue-600 bg-blue-100'
      case 'recommendation': return 'text-green-600 bg-green-100'
      case 'optimization': return 'text-purple-600 bg-purple-100'
      case 'alert': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'attendance': return <Users className="h-4 w-4" />
      case 'parking': return <Car className="h-4 w-4" />
      case 'rooms': return <Calendar className="h-4 w-4" />
      default: return <Brain className="h-4 w-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Navigation />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">AI Insights & Predictions</h2>
          <p className="text-gray-600">Smart recommendations and predictions to optimize your office operations</p>
          <div className="mt-4 text-sm text-gray-500">
            {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
          </div>
        </div>

        {/* AI Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Insights</CardTitle>
              <Brain className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{insights.length}</div>
              <p className="text-xs text-muted-foreground">AI-generated recommendations</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Prediction Accuracy</CardTitle>
              <Target className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">89%</div>
              <p className="text-xs text-muted-foreground">Last 30 days average</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Optimizations Applied</CardTitle>
              <CheckCircle className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">23</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">4.2h</div>
              <p className="text-xs text-muted-foreground">Per employee/week</p>
            </CardContent>
          </Card>
        </div>

        {/* AI Insights */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Brain className="h-5 w-5 mr-2" />
              Current AI Insights
            </CardTitle>
            <CardDescription>
              Smart recommendations based on data analysis and machine learning
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.map((insight) => (
                <div key={insight.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full ${getInsightColor(insight.type)}`}>
                        {getInsightIcon(insight.type)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{insight.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(insight.impact)}`}>
                            {insight.impact.toUpperCase()} IMPACT
                          </span>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            {getCategoryIcon(insight.category)}
                            <span className="capitalize">{insight.category}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">{insight.confidence}%</div>
                      <div className="text-xs text-gray-500">Confidence</div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-3">{insight.description}</p>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="sm">
                      Dismiss
                    </Button>
                    <Button size="sm">
                      Apply Suggestion
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Predictions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Attendance Predictions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Next Week Attendance Prediction
              </CardTitle>
              <CardDescription>
                AI-powered attendance forecasting based on historical data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {predictions.nextWeekAttendance.map((day) => (
                  <div key={day.day} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900 w-12">{day.day}</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2 w-32">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${day.predicted}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{day.predicted}%</div>
                      <div className="text-xs text-gray-500">{day.confidence}% conf.</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Parking Predictions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Car className="h-5 w-5 mr-2" />
                Parking Utilization Forecast
              </CardTitle>
              <CardDescription>
                Peak hours prediction for optimal parking management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {predictions.parkingPeakHours.map((hour) => (
                  <div key={hour.hour} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900 w-20">{hour.hour}</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2 w-32">
                        <div 
                          className={`h-2 rounded-full ${
                            hour.utilization > 90 ? 'bg-red-500' :
                            hour.utilization > 70 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${hour.utilization}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{hour.utilization}%</div>
                      <div className="text-xs text-gray-500">
                        {hour.utilization > 90 ? 'High' : hour.utilization > 70 ? 'Medium' : 'Low'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              AI Configuration
            </CardTitle>
            <CardDescription>
              Customize AI features and notification preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Prediction Features</h4>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Attendance forecasting</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Parking optimization</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Room recommendations</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">Peak day suggestions</span>
                  </label>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-3">Auto-Resolution</h4>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm">Double booking resolution</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">No-show room release</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">Parking reallocation</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">Attendance reminders</span>
                  </label>
                </div>
              </div>
            </div>
            <div className="mt-6 pt-6 border-t">
              <Button>Save AI Preferences</Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
